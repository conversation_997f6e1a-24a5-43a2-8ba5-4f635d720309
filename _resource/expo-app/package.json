{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "test": "jest --watchAll", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "^0.10.1", "@expo/vector-icons": "^14.0.0", "@gluestack-ui/accordion": "^1.0.6", "@gluestack-ui/actionsheet": "^0.2.44", "@gluestack-ui/alert": "^0.1.15", "@gluestack-ui/alert-dialog": "^0.1.30", "@gluestack-ui/avatar": "^0.1.17", "@gluestack-ui/button": "^1.0.7", "@gluestack-ui/checkbox": "^0.1.31", "@gluestack-ui/divider": "^0.1.9", "@gluestack-ui/fab": "^0.1.21", "@gluestack-ui/form-control": "^0.1.18", "@gluestack-ui/icon": "^0.1.22", "@gluestack-ui/image": "^0.1.10", "@gluestack-ui/input": "^0.1.31", "@gluestack-ui/link": "^0.1.22", "@gluestack-ui/menu": "^0.2.36", "@gluestack-ui/modal": "^0.1.34", "@gluestack-ui/nativewind-utils": "latest", "@gluestack-ui/overlay": "latest", "@gluestack-ui/popover": "^0.1.37", "@gluestack-ui/pressable": "^0.1.16", "@gluestack-ui/progress": "^0.1.16", "@gluestack-ui/radio": "^0.1.32", "@gluestack-ui/select": "^0.1.29", "@gluestack-ui/slider": "^0.1.25", "@gluestack-ui/spinner": "^0.1.14", "@gluestack-ui/switch": "^0.1.22", "@gluestack-ui/textarea": "^0.1.23", "@gluestack-ui/toast": "^1.0.7", "@gluestack-ui/tooltip": "^0.1.32", "@hookform/resolvers": "^3.3.4", "@legendapp/motion": "^2.4.0", "@react-navigation/native": "^6.0.2", "@unitools/image": "^0.0.4", "@unitools/image-expo": "^0.0.5", "@unitools/link": "^0.0.3", "@unitools/link-expo": "^0.0.1", "@unitools/navigation": "^0.0.1-alpha.0", "@unitools/router": "^0.0.4", "@unitools/router-expo": "^0.0.1", "expo": "~51.0.6", "expo-font": "~12.0.5", "expo-image": "~1.12.9", "expo-linking": "~6.3.1", "expo-router": "~3.5.14", "expo-splash-screen": "~0.27.4", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.4", "expo-updates": "~0.25.12", "expo-web-browser": "~13.0.3", "lucide-react-native": "^0.378.0", "nativewind": "4.0.36", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.51.4", "react-native": "0.74.1", "react-native-reanimated": "latest", "react-native-safe-area-context": "4.10.1", "react-native-screens": "3.31.1", "react-native-svg": "^15.2.0", "react-native-web": "~0.19.6", "tailwindcss": "3.4.3", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "~18.2.45", "@types/react-native": "^0.73.0", "@unitools/babel-plugin-universal-image": "^1.0.0", "autoprefixer": "^10.4.20", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~51.0.1", "jscodeshift": "0.15.2", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}