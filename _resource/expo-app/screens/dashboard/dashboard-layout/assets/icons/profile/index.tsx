import { createIcon } from "@gluestack-ui/icon";
import { Path, G, Svg } from "react-native-svg";
export const ProfileIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 65 32",
  path: (
    <G>
      <Path
        d="M38.2541 18.0002C39.4961 18.0002 40.5029 19.007 40.5029 20.2491V20.8245C40.5029 21.7188 40.1833 22.5836 39.6018 23.263C38.0324 25.0965 35.6453 26.0013 32.4999 26.0013C29.354 26.0013 26.968 25.0962 25.4017 23.2619C24.8219 22.583 24.5034 21.7195 24.5034 20.8267V20.2491C24.5034 19.007 25.5103 18.0002 26.7523 18.0002H38.2541ZM38.2541 19.5002H26.7523C26.3387 19.5002 26.0034 19.8355 26.0034 20.2491V20.8267C26.0034 21.3624 26.1945 21.8805 26.5424 22.2878C27.7957 23.7555 29.7616 24.5013 32.4999 24.5013C35.2382 24.5013 37.2058 23.7555 38.4623 22.2876C38.8112 21.8799 39.0029 21.361 39.0029 20.8245V20.2491C39.0029 19.8355 38.6676 19.5002 38.2541 19.5002ZM32.4999 6.00488C35.2613 6.00488 37.4999 8.24346 37.4999 11.0049C37.4999 13.7663 35.2613 16.0049 32.4999 16.0049C29.7385 16.0049 27.4999 13.7663 27.4999 11.0049C27.4999 8.24346 29.7385 6.00488 32.4999 6.00488ZM32.4999 7.50488C30.5669 7.50488 28.9999 9.07189 28.9999 11.0049C28.9999 12.9379 30.5669 14.5049 32.4999 14.5049C34.4329 14.5049 35.9999 12.9379 35.9999 11.0049C35.9999 9.07189 34.4329 7.50488 32.4999 7.50488Z"
        fill="#8E8E8E"
      />
    </G>
  ),
});
