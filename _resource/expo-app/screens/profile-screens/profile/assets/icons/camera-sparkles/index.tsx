import { createIcon } from "@gluestack-ui/icon";
import { Path, G, Svg } from "react-native-svg";
export const CameraSparklesIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 16 16",
  path: (
    <G>
      <Path
        d="M10.8777 0.282251L11.226 1.35305C11.3343 1.67859 11.5171 1.97441 11.7599 2.21699C12.0026 2.45957 12.2987 2.64223 12.6245 2.75044L13.6961 3.09845L13.7175 3.10381C13.8001 3.13292 13.8716 3.18692 13.9222 3.25836C13.9728 3.3298 14 3.41516 14 3.50268C14 3.5902 13.9728 3.67556 13.9222 3.747C13.8716 3.81844 13.8001 3.87244 13.7175 3.90155L12.6459 4.24956C12.3201 4.35778 12.0241 4.54044 11.7813 4.78302C11.5385 5.0256 11.3558 5.32141 11.2475 5.64696L10.8992 6.71775C10.87 6.80029 10.816 6.87175 10.7445 6.92231C10.673 6.97286 10.5876 7.00001 10.5 7.00001C10.4124 7.00001 10.327 6.97286 10.2555 6.92231C10.2484 6.91732 10.2416 6.91213 10.2349 6.90675C10.1736 6.85755 10.1271 6.79215 10.1008 6.71775L9.75255 5.64696C9.73383 5.59014 9.71284 5.53422 9.68966 5.47932C9.57963 5.21872 9.42023 4.98116 9.21968 4.77999C9.18154 4.74173 9.14208 4.70496 9.10139 4.66973C8.88302 4.48064 8.62919 4.33588 8.35408 4.24421L7.28247 3.8962C7.19987 3.86708 7.12835 3.81308 7.07776 3.74164C7.02717 3.6702 7 3.58484 7 3.49733C7 3.40981 7.02717 3.32445 7.07776 3.25301C7.12835 3.18157 7.19987 3.12757 7.28247 3.09845L8.35408 2.75044C8.67591 2.63941 8.96768 2.45549 9.20656 2.21307C9.44544 1.97065 9.62497 1.67631 9.73111 1.35305L10.0794 0.282251C10.1085 0.19972 10.1626 0.128252 10.2341 0.0776997C10.3056 0.0271473 10.391 0 10.4786 0C10.5662 0 10.6516 0.0271473 10.7231 0.0776997C10.7946 0.128252 10.8486 0.19972 10.8777 0.282251ZM15.7829 8.21319L15.0175 7.96461C14.7848 7.88731 14.5733 7.75684 14.3999 7.58357C14.2265 7.4103 14.0959 7.199 14.0186 6.96647L13.7698 6.20161C13.749 6.14266 13.7104 6.09161 13.6593 6.05551C13.6083 6.0194 13.5473 6.00001 13.4847 6.00001C13.4221 6.00001 13.3611 6.0194 13.31 6.05551C13.259 6.09161 13.2204 6.14266 13.1996 6.20161L12.9508 6.96647C12.875 7.19737 12.7467 7.40762 12.5761 7.58077C12.4055 7.75392 12.1971 7.8853 11.9672 7.96461L11.2018 8.21319C11.1428 8.23398 11.0917 8.27255 11.0555 8.32358C11.0194 8.37461 11 8.43558 11 8.4981C11 8.56061 11.0194 8.62158 11.0555 8.67261C11.0917 8.72364 11.1428 8.76221 11.2018 8.78301L11.9672 9.03158C12.2003 9.10926 12.412 9.24032 12.5855 9.41428C12.7589 9.58824 12.8893 9.80031 12.9661 10.0335L13.2149 10.7984C13.2357 10.8574 13.2743 10.9084 13.3254 10.9445C13.3764 10.9806 13.4374 11 13.5 11C13.5626 11 13.6236 10.9806 13.6746 10.9445C13.7257 10.9084 13.7643 10.8574 13.7851 10.7984L14.0339 10.0335C14.1113 9.80101 14.2418 9.58972 14.4152 9.41645C14.5886 9.24317 14.8001 9.11271 15.0328 9.03541L15.7982 8.78683C15.8572 8.76603 15.9083 8.72746 15.9445 8.67643C15.9806 8.62541 16 8.56443 16 8.50192C16 8.43941 15.9806 8.37844 15.9445 8.32741C15.9083 8.27638 15.8572 8.23781 15.7982 8.21701L15.7829 8.21319ZM13.5018 12C13.3276 11.9993 13.1576 11.9654 13 11.9008V12C13 12.5523 12.5523 13 12 13H4C3.44772 13 3 12.5523 3 12V6.99997C3 6.44769 3.44772 5.99997 4 5.99997H5C5.18939 5.99997 5.36252 5.89297 5.44721 5.72358L6.17082 4.27637C6.17967 4.25867 6.18948 4.24166 6.20017 4.22538C6.06896 4.00656 6 3.75899 6 3.49608C6 3.36317 6.01762 3.23418 6.052 3.11084C5.72067 3.24591 5.44211 3.49773 5.27639 3.82915L4.69098 4.99997H4C2.89543 4.99997 2 5.8954 2 6.99997V12C2 13.1045 2.89543 14 4 14H12C13.1046 14 14 13.1045 14 12V11.921C13.8433 11.9785 13.6746 11.9996 13.5018 12ZM8.73574 6.09085L9.0529 7.05186C9.1063 7.20028 9.18431 7.34167 9.29528 7.47603C8.94631 7.17913 8.49408 6.99997 8 6.99997C6.89543 6.99997 6 7.8954 6 8.99997C6 10.1045 6.89543 11 8 11C9.10457 11 10 10.1045 10 8.99997L10 8.9983C10.0372 9.08842 10.0852 9.17463 10.1439 9.25546C10.304 9.47581 10.5242 9.63608 10.7945 9.73624L10.9 9.77097C10.5597 11.0542 9.39031 12 8 12C6.34315 12 5 10.6568 5 8.99997C5 7.34312 6.34315 5.99997 8 5.99997C8.25384 5.99997 8.50032 6.0315 8.73574 6.09085Z"
        fill="#F6F6F6"
      />
    </G>
  ),
});
