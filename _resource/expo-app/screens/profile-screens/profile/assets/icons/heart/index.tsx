import { createIcon } from "@gluestack-ui/icon";
import { Path, G, Svg } from "react-native-svg";
export const HeartIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 25 24",
  path: (
    <G>
      <Path
        d="M13.3197 5.57961L12.499 6.40211L11.6757 5.57886C9.57663 3.4798 6.17337 3.4798 4.0743 5.57886C1.97523 7.67793 1.97523 11.0812 4.0743 13.1803L11.9697 21.0756C12.2626 21.3685 12.7374 21.3685 13.0303 21.0756L20.9318 13.1788C23.0262 11.0728 23.0298 7.67906 20.9303 5.57961C18.8274 3.47672 15.4226 3.47672 13.3197 5.57961ZM19.8682 12.1211L12.5 19.4846L5.13496 12.1196C3.62168 10.6063 3.62168 8.15281 5.13496 6.63952C6.64824 5.12624 9.10176 5.12624 10.615 6.63952L11.9725 7.99697C12.2703 8.29483 12.755 8.28903 13.0457 7.98412L14.3803 6.64027C15.8974 5.12317 18.3526 5.12316 19.8697 6.64027C21.3833 8.15391 21.3807 10.6001 19.8682 12.1211Z"
        fill="#8E8E8E"
      />
    </G>
  ),
});
