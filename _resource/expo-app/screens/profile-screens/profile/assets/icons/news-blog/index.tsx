import { createIcon } from "@gluestack-ui/icon";
import { Path, G, Svg } from "react-native-svg";
export const NewsBlogIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 16 17",
  path: (
    <G>
      <Path
        d="M3.5 5.5C3.22386 5.5 3 5.72386 3 6C3 6.27614 3.22386 6.5 3.5 6.5H10.5C10.7761 6.5 11 6.27614 11 6C11 5.72386 10.7761 5.5 10.5 5.5H3.5ZM3.5 7.5C3.22386 7.5 3 7.72386 3 8V10C3 10.2761 3.22386 10.5 3.5 10.5H5.5C5.77614 10.5 6 10.2761 6 10V8C6 7.72386 5.77614 7.5 5.5 7.5H3.5ZM4 9.5V8.5H5V9.5H4ZM7.5 7.5C7.22386 7.5 7 7.72386 7 8C7 8.27614 7.22386 8.5 7.5 8.5H10.5C10.7761 8.5 11 8.27614 11 8C11 7.72386 10.7761 7.5 10.5 7.5H7.5ZM7.5 9.5C7.22386 9.5 7 9.72386 7 10C7 10.2761 7.22386 10.5 7.5 10.5H10.5C10.7761 10.5 11 10.2761 11 10C11 9.72386 10.7761 9.5 10.5 9.5H7.5ZM1 4.5C1 3.39543 1.89543 2.5 3 2.5H11C12.1046 2.5 13 3.39543 13 4.5C14.1046 4.5 15 5.39543 15 6.5V11C15 12.3807 13.8807 13.5 12.5 13.5H3.5C2.11929 13.5 1 12.3807 1 11V4.5ZM12.5 11C12.2239 11 12 10.7761 12 10.5V4.5C12 3.94772 11.5523 3.5 11 3.5H3C2.44772 3.5 2 3.94772 2 4.5V11C2 11.8284 2.67157 12.5 3.5 12.5H12.5C13.3284 12.5 14 11.8284 14 11V6.5C14 5.94772 13.5523 5.5 13 5.5V10.5C13 10.7761 12.7761 11 12.5 11Z"
        fill="#414040"
      />
    </G>
  ),
});
