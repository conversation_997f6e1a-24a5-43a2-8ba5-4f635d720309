import { createIcon } from "@gluestack-ui/icon";
import { Path, G, Svg } from "react-native-svg";
export const ProfileIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 25 24",
  path: (
    <G>
      <Path
        d="M18.2541 13.9997C19.4961 13.9997 20.5029 15.0065 20.5029 16.2486V16.824C20.5029 17.7183 20.1833 18.5831 19.6018 19.2625C18.0324 21.096 15.6453 22.0008 12.4999 22.0008C9.35401 22.0008 6.968 21.0957 5.4017 19.2614C4.82194 18.5825 4.50342 17.719 4.50342 16.8262V16.2486C4.50342 15.0065 5.51027 13.9997 6.75229 13.9997H18.2541ZM18.2541 15.4997H6.75229C6.3387 15.4997 6.00342 15.835 6.00342 16.2486V16.8262C6.00342 17.3619 6.19453 17.88 6.54239 18.2874C7.79569 19.755 9.76157 20.5008 12.4999 20.5008C15.2382 20.5008 17.2058 19.755 18.4623 18.2871C18.8112 17.8795 19.0029 17.3605 19.0029 16.824V16.2486C19.0029 15.835 18.6676 15.4997 18.2541 15.4997ZM12.4999 2.00439C15.2613 2.00439 17.4999 4.24297 17.4999 7.00439C17.4999 9.76582 15.2613 12.0044 12.4999 12.0044C9.73845 12.0044 7.49988 9.76582 7.49988 7.00439C7.49988 4.24297 9.73845 2.00439 12.4999 2.00439ZM12.4999 3.50439C10.5669 3.50439 8.99988 5.0714 8.99988 7.00439C8.99988 8.93739 10.5669 10.5044 12.4999 10.5044C14.4329 10.5044 15.9999 8.93739 15.9999 7.00439C15.9999 5.0714 14.4329 3.50439 12.4999 3.50439Z"
        fill="#8E8E8E"
      />
    </G>
  ),
});
