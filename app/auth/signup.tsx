import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { Button, ButtonText } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import { FormControl } from "@/components/ui/form-control";
import { AuthLayout } from "@/screens/auth/layout";
import { useRouter } from "expo-router";

const SignUpScreen = () => {
  const router = useRouter();

  return (
    <AuthLayout>
      <VStack
        className="w-full max-w-[440px] items-center justify-center flex-1"
        space="xl"
      >
        <VStack className="items-center" space="md">
          <Heading size="2xl" className="text-typography-900 font-bold text-center">
            注册账号
          </Heading>
          <Text size="md" className="text-typography-600 text-center">
            创建您的新账号，开始使用我们的服务
          </Text>
        </VStack>

        <VStack className="w-full" space="lg">
          <FormControl>
            <Input>
              <InputField placeholder="用户名" />
            </Input>
          </FormControl>

          <FormControl>
            <Input>
              <InputField placeholder="邮箱地址" />
            </Input>
          </FormControl>

          <FormControl>
            <Input>
              <InputField placeholder="密码" secureTextEntry />
            </Input>
          </FormControl>

          <FormControl>
            <Input>
              <InputField placeholder="确认密码" secureTextEntry />
            </Input>
          </FormControl>

          <Button className="w-full">
            <ButtonText>注册</ButtonText>
          </Button>

          <Button
            variant="link"
            onPress={() => router.push("/auth/splash-screen")}
          >
            <ButtonText>返回首页</ButtonText>
          </Button>
        </VStack>
      </VStack>
    </AuthLayout>
  );
};

export default SignUpScreen;
