import React, { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { SplashScreen } from "@/screens/auth/splash-screen";

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // 等待状态加载完成后进行路由判断
    if (!isLoading) {
      if (isAuthenticated) {
        // 如果已登录，跳转到home页面
        router.replace('/home');
      } else {
        // 如果未登录，跳转到登录页面
        router.replace('/auth/signin');
      }
    }
  }, [isAuthenticated, isLoading, router]);

  // 在状态加载期间显示启动屏幕
  return <SplashScreen />;
}
