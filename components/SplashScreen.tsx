import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { Center } from '@/components/ui/center';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { Spinner } from '@/components/ui/spinner';
import { Box } from '@/components/ui/box';
import { Image } from '@/components/ui/image';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationFinish?: () => void;
}

export const SplashScreen: React.FC<SplashScreenProps> = ({ onAnimationFinish }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // 启动动画序列
    const animationSequence = Animated.sequence([
      // Logo 淡入和缩放动画
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      // 文字滑入动画
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      // 等待一段时间
      Animated.delay(1500),
    ]);

    animationSequence.start(() => {
      // 动画完成后的回调
      if (onAnimationFinish) {
        onAnimationFinish();
      }
    });
  }, [fadeAnim, scaleAnim, slideAnim, onAnimationFinish]);

  return (
    <Box className="flex-1 bg-background-0">
      <Center className="flex-1">
        <VStack space="2xl" className="items-center">
          {/* Logo 区域 */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            }}
          >
            <Box className="w-40 h-40 rounded-3xl bg-primary-500 items-center justify-center shadow-2xl">
              {/* 这里可以放置您的 Logo 图片 */}
              <VStack space="xs" className="items-center">
                <Text className="text-5xl font-bold text-white">
                  🚀
                </Text>
                <Text className="text-lg font-semibold text-white">
                  应用
                </Text>
              </VStack>
            </Box>
          </Animated.View>

          {/* 应用名称和标语 */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            <VStack space="lg" className="items-center px-8">
              <Heading size="3xl" className="text-typography-900 font-bold text-center">
                欢迎使用
              </Heading>
              <Heading size="2xl" className="text-primary-600 font-bold text-center">
                Glue App
              </Heading>
              <Text size="lg" className="text-typography-600 text-center max-w-sm leading-relaxed">
                基于 Gluestack UI 构建的现代化应用
              </Text>
              <Text size="md" className="text-typography-500 text-center max-w-xs">
                为您提供最佳的用户体验
              </Text>
            </VStack>
          </Animated.View>

          {/* 加载指示器 */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            }}
          >
            <VStack space="md" className="items-center mt-8">
              <Spinner size="large" className="text-primary-500" />
              <Text size="sm" className="text-typography-500">
                正在加载...
              </Text>
            </VStack>
          </Animated.View>
        </VStack>

        {/* 底部版权信息 */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            position: 'absolute',
            bottom: 60,
          }}
        >
          <VStack space="xs" className="items-center">
            <Text size="sm" className="text-typography-500 text-center font-medium">
              Powered by Gluestack UI
            </Text>
            <Text size="xs" className="text-typography-400 text-center">
              © 2024 您的公司名称. 保留所有权利.
            </Text>
          </VStack>
        </Animated.View>
      </Center>
    </Box>
  );
};

export default SplashScreen;
