import React, { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // 如果没有登录且不在加载状态，重定向到登录页面
    if (!isAuthenticated && !isLoading) {
      router.replace('/auth/signin');
    }
  }, [isAuthenticated, isLoading, router]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <VStack className="flex-1 justify-center items-center bg-background-0">
        <Spinner size="large" />
        <Text className="mt-4 text-typography-600">加载中...</Text>
      </VStack>
    );
  }

  // 如果未登录，显示空白页面（实际上会被重定向）
  if (!isAuthenticated) {
    return (
      <VStack className="flex-1 justify-center items-center bg-background-0">
        <Text className="text-typography-600">正在跳转到登录页面...</Text>
      </VStack>
    );
  }

  // 如果已登录，显示子组件
  return <>{children}</>;
};
