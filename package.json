{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@expo/vector-icons": "^14.0.2", "@gluestack-ui/accordion": "^1.0.8", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.32", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.8", "@gluestack-ui/checkbox": "^0.1.33", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.22", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/image": "^0.1.11", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/link": "^0.1.23", "@gluestack-ui/menu": "^0.2.37", "@gluestack-ui/modal": "^0.1.35", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.16", "@gluestack-ui/popover": "^0.1.43", "@gluestack-ui/pressable": "^0.1.17", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.34", "@gluestack-ui/select": "^0.1.30", "@gluestack-ui/slider": "^0.1.26", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.23", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/toast": "^1.0.8", "@gluestack-ui/tooltip": "^0.1.38", "@legendapp/motion": "^2.4.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "babel-plugin-module-resolver": "^5.0.2", "expo": "~52.0.25", "expo-blur": "~14.0.2", "expo-constants": "~17.0.4", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.4", "expo-router": "~4.0.16", "expo-splash-screen": "~0.29.20", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.1", "expo-system-ui": "~4.0.7", "expo-web-browser": "~14.0.2", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.6", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "jscodeshift": "^0.15.2", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}