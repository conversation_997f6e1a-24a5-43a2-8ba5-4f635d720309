import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { ScrollView } from "@/components/ui/scroll-view";
import { Image } from "@/components/ui/image";

type AuthLayoutProps = {
  children: React.ReactNode;
};

export const AuthLayout = (props: AuthLayoutProps) => {
  return (
    <SafeAreaView className="w-full h-full bg-background-0">
      <ScrollView
        className="w-full h-full"
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <HStack className="w-full h-full min-h-screen">
          {/* 左侧背景 - 仅在桌面端显示 */}
          <VStack
            className="relative hidden md:flex h-full w-full flex-1 items-center justify-center"
            space="md"
          >
            <Image
              height="100%"
              width="100%"
              source={require("@/assets/images/radialGradient.png")}
              className="object-cover h-full w-full"
              alt="Radial Gradient"
            />
          </VStack>

          {/* 右侧内容区域 */}
          <VStack className="flex-1 w-full h-full min-h-screen items-center justify-center p-6 md:p-9">
            <VStack className="w-full max-w-md items-center justify-center flex-1" space="2xl">
              {props.children}
            </VStack>
          </VStack>
        </HStack>
      </ScrollView>
    </SafeAreaView>
  );
};
