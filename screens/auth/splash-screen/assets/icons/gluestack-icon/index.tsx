import { createIcon } from "@/components/ui/icon";
import { Path, G, Svg } from "react-native-svg";

export const GluestackIcon: any = createIcon({
  Root: Svg,
  viewBox: "0 0 256 256",
  path: (
    <G>
      <Path
        d="M128 0C57.3076 0 0 57.3076 0 128C0 198.692 57.3076 256 128 256C198.692 256 256 198.692 256 128C256 57.3076 198.692 0 128 0Z"
        fill="url(#paint0_linear_1_2)"
      />
      <Path
        d="M128 32C74.9807 32 32 74.9807 32 128C32 181.019 74.9807 224 128 224C181.019 224 224 181.019 224 128C224 74.9807 181.019 32 128 32Z"
        fill="url(#paint1_linear_1_2)"
      />
      <Path
        d="M128 64C92.6538 64 64 92.6538 64 128C64 163.346 92.6538 192 128 192C163.346 192 192 163.346 192 128C192 92.6538 163.346 64 128 64Z"
        fill="url(#paint2_linear_1_2)"
      />
      <Path
        d="M128 96C110.327 96 96 110.327 96 128C96 145.673 110.327 160 128 160C145.673 160 160 145.673 160 128C160 110.327 145.673 96 128 96Z"
        fill="url(#paint3_linear_1_2)"
      />
    </G>
  ),
});
