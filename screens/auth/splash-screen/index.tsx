import { VStack } from "@/components/ui/vstack";
import { Button, ButtonText } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import { useColorScheme } from "nativewind";
import { useRouter } from "expo-router";
import { AuthLayout } from "../layout";
import { GluestackIcon } from "./assets/icons/gluestack-icon";

// 中文应用 Logo 组件
const ChineseAppLogo = () => {
  const { colorScheme } = useColorScheme();
  
  return (
    <VStack className="items-center" space="md">
      {/* Logo 容器 */}
      <Box className="w-20 h-20 rounded-2xl bg-primary-500 items-center justify-center shadow-lg">
        <Text className="text-3xl font-bold text-white">
          应
        </Text>
      </Box>
      
      {/* 应用名称 */}
      <VStack className="items-center" space="xs">
        <Heading 
          size="2xl" 
          className={`font-bold text-center ${
            colorScheme === "dark" ? "text-typography-0" : "text-typography-900"
          }`}
        >
          Glue 应用
        </Heading>
        <Text 
          size="md" 
          className={`text-center ${
            colorScheme === "dark" ? "text-typography-400" : "text-typography-600"
          }`}
        >
          基于 Gluestack UI 构建
        </Text>
      </VStack>
    </VStack>
  );
};

const SplashScreenWithLeftBackground = () => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();

  return (
    <VStack
      className="w-full max-w-[440px] items-center h-full justify-center"
      space="2xl"
    >
      {/* 中文 Logo */}
      <ChineseAppLogo />

      {/* 欢迎文字 */}
      <VStack className="items-center" space="md">
        <Heading
          size="xl"
          className={`text-center font-medium ${
            colorScheme === "dark" ? "text-typography-100" : "text-typography-800"
          }`}
        >
          欢迎使用我们的应用
        </Heading>
        <Text
          size="sm"
          className={`text-center max-w-xs leading-relaxed ${
            colorScheme === "dark" ? "text-typography-400" : "text-typography-600"
          }`}
        >
          为您提供最佳的用户体验，让工作更加高效便捷
        </Text>
      </VStack>

      {/* 按钮组 */}
      <VStack className="w-full" space="lg">
        <Button
          className="w-full"
          onPress={() => {
            router.push("/auth/signin");
          }}
        >
          <ButtonText className="font-medium">登录</ButtonText>
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onPress={() => {
            router.push("/auth/signup");
          }}
        >
          <ButtonText className="font-medium">注册账号</ButtonText>
        </Button>
      </VStack>

      {/* 底部提示文字 */}
      <Text
        size="xs"
        className={`text-center ${
          colorScheme === "dark" ? "text-typography-500" : "text-typography-500"
        }`}
      >
        继续使用即表示您同意我们的服务条款和隐私政策
      </Text>
    </VStack>
  );
};

export const SplashScreen = () => {
  return (
    <AuthLayout>
      <SplashScreenWithLeftBackground />
    </AuthLayout>
  );
};
