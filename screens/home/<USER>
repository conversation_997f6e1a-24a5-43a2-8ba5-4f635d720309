import React, { useState, useRef, useEffect } from "react";
import { ScrollView, Dimensions, Platform, NativeScrollEvent, NativeSyntheticEvent } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Box } from "@/components/ui/box";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { Pressable } from "@/components/ui/pressable";

// 轮播图片数据 - 使用可靠的图片源
const carouselData = [
  {
    id: 1,
    image: "https://picsum.photos/800/400?random=1",
    title: "欢迎使用我们的应用",
    description: "发现更多精彩内容"
  },
  {
    id: 2,
    image: "https://picsum.photos/800/400?random=2",
    title: "创新科技体验",
    description: "让生活更加便捷"
  },
  {
    id: 3,
    image: "https://picsum.photos/800/400?random=3",
    title: "智能解决方案",
    description: "为您提供最佳服务"
  }
];

const { width: screenWidth } = Dimensions.get("window");

const ImageCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % carouselData.length;
      setCurrentIndex(nextIndex);
      scrollViewRef.current?.scrollTo({
        x: nextIndex * screenWidth,
        animated: true,
      });
    }, 4000); // 4秒切换一次

    return () => clearInterval(interval);
  }, [currentIndex]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const index = Math.round(contentOffset.x / screenWidth);
    setCurrentIndex(index);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  return (
    <VStack className="w-full">
      {/* 轮播图片区域 */}
      <Box className="relative w-full h-64 md:h-80">
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          className="w-full h-full"
        >
          {carouselData.map((item, index) => (
            <Box key={item.id} className="relative" style={{ width: screenWidth, height: '100%' }}>
              <Image
                source={{ uri: item.image }}
                style={{ width: screenWidth, height: '100%' }}
                className="object-cover"
                alt={item.title}
                resizeMode="cover"
              />
              {/* 渐变遮罩 */}
              <Box className="absolute inset-0 bg-black/40" />

              {/* 文字内容 */}
              <VStack className="absolute bottom-6 left-6 right-6" space="sm">
                <Heading size="xl" className="text-white font-bold">
                  {item.title}
                </Heading>
                <Text className="text-white/90 text-base">
                  {item.description}
                </Text>
              </VStack>
            </Box>
          ))}
        </ScrollView>

        {/* 指示器 - 3个点用于手动控制 */}
        <HStack className="absolute bottom-6 self-center bg-black/30 px-3 py-2 rounded-full" space="sm">
          {carouselData.map((_, index) => (
            <Pressable
              key={index}
              onPress={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? "bg-white scale-110"
                  : "bg-white/60 hover:bg-white/80"
              }`}
            />
          ))}
        </HStack>
      </Box>
    </VStack>
  );
};

const HomeScreen = () => {
  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* 图片轮播 */}
        <ImageCarousel />
        
        {/* 其他内容区域 */}
        <VStack className="p-6" space="xl">
          <VStack space="md">
            <Heading size="2xl" className="text-typography-900">
              热门推荐
            </Heading>
            <Text className="text-typography-700">
              为您精选的优质内容
            </Text>
          </VStack>

          {/* 示例内容卡片 */}
          <VStack space="md">
            {[1, 2, 3].map((item) => (
              <Box
                key={item}
                className="p-4 bg-background-50 rounded-lg border border-border-200"
              >
                <VStack space="sm">
                  <Heading size="md" className="text-typography-900">
                    内容标题 {item}
                  </Heading>
                  <Text className="text-typography-700">
                    这里是内容描述，展示一些有趣的信息和功能介绍。
                  </Text>
                </VStack>
              </Box>
            ))}
          </VStack>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
};

export { HomeScreen };
