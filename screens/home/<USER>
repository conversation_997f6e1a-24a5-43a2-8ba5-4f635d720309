import React, { useState, useRef, useEffect } from "react";
import { ScrollView, Dimensions, Platform, NativeScrollEvent, NativeSyntheticEvent } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Box } from "@/components/ui/box";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { SafeAreaView } from "@/components/ui/safe-area-view";
import { Pressable } from "@/components/ui/pressable";

// 轮播图片数据
const carouselData = [
  {
    id: 1,
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop",
    title: "欢迎使用我们的应用",
    description: "发现更多精彩内容"
  },
  {
    id: 2,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
    title: "创新科技体验",
    description: "让生活更加便捷"
  },
  {
    id: 3,
    image: "https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?w=800&h=400&fit=crop",
    title: "智能解决方案",
    description: "为您提供最佳服务"
  },
  {
    id: 4,
    image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop",
    title: "连接未来",
    description: "开启数字化新时代"
  }
];

const { width: screenWidth } = Dimensions.get("window");

const ImageCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % carouselData.length;
      setCurrentIndex(nextIndex);
      scrollViewRef.current?.scrollTo({
        x: nextIndex * screenWidth,
        animated: true,
      });
    }, 4000); // 4秒切换一次

    return () => clearInterval(interval);
  }, [currentIndex]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const index = Math.round(contentOffset.x / screenWidth);
    setCurrentIndex(index);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  return (
    <VStack className="w-full">
      {/* 轮播图片区域 */}
      <Box className="relative w-full h-64 md:h-80">
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          className="w-full h-full"
        >
          {carouselData.map((item, index) => (
            <Box key={item.id} className="relative" style={{ width: screenWidth }}>
              <Image
                source={{ uri: item.image }}
                className="w-full h-full object-cover"
                alt={item.title}
              />
              {/* 渐变遮罩 */}
              <Box className="absolute inset-0 bg-black/40" />
              
              {/* 文字内容 */}
              <VStack className="absolute bottom-6 left-6 right-6" space="sm">
                <Heading size="xl" className="text-white font-bold">
                  {item.title}
                </Heading>
                <Text className="text-white/90 text-base">
                  {item.description}
                </Text>
              </VStack>
            </Box>
          ))}
        </ScrollView>

        {/* 指示器 */}
        <HStack className="absolute bottom-4 self-center" space="xs">
          {carouselData.map((_, index) => (
            <Pressable
              key={index}
              onPress={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full ${
                index === currentIndex ? "bg-white" : "bg-white/50"
              }`}
            />
          ))}
        </HStack>
      </Box>
    </VStack>
  );
};

const HomeScreen = () => {
  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* 图片轮播 */}
        <ImageCarousel />
        
        {/* 其他内容区域 */}
        <VStack className="p-6" space="xl">
          <VStack space="md">
            <Heading size="2xl" className="text-typography-900">
              热门推荐
            </Heading>
            <Text className="text-typography-700">
              为您精选的优质内容
            </Text>
          </VStack>

          {/* 示例内容卡片 */}
          <VStack space="md">
            {[1, 2, 3].map((item) => (
              <Box
                key={item}
                className="p-4 bg-background-50 rounded-lg border border-border-200"
              >
                <VStack space="sm">
                  <Heading size="md" className="text-typography-900">
                    内容标题 {item}
                  </Heading>
                  <Text className="text-typography-700">
                    这里是内容描述，展示一些有趣的信息和功能介绍。
                  </Text>
                </VStack>
              </Box>
            ))}
          </VStack>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
};

export { HomeScreen };
