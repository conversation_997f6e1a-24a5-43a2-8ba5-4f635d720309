import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 用户信息类型
export interface User {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
}

// 认证状态类型
interface AuthState {
  // 状态
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
  
  // 操作方法
  login: (username: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (user: Partial<User>) => void;
}

// 模拟用户数据
const MOCK_USERS = [
  {
    id: '1',
    username: 'admin',
    password: '123456',
    email: '<EMAIL>',
    avatar: 'https://via.placeholder.com/100x100/4F46E5/FFFFFF?text=A'
  }
];

// 模拟登录API
const mockLogin = async (username: string, password: string): Promise<{ success: boolean; user?: User; token?: string; message: string }> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const user = MOCK_USERS.find(u => u.username === username && u.password === password);
  
  if (user) {
    const { password: _, ...userWithoutPassword } = user;
    return {
      success: true,
      user: userWithoutPassword,
      token: `mock_token_${Date.now()}`,
      message: '登录成功'
    };
  } else {
    return {
      success: false,
      message: '用户名或密码错误'
    };
  }
};

// 创建认证store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      isLoading: false,
      user: null,
      token: null,

      // 登录方法
      login: async (username: string, password: string) => {
        set({ isLoading: true });
        
        try {
          const result = await mockLogin(username, password);
          
          if (result.success && result.user && result.token) {
            set({
              isAuthenticated: true,
              user: result.user,
              token: result.token,
              isLoading: false
            });
            return { success: true, message: result.message };
          } else {
            set({ isLoading: false });
            return { success: false, message: result.message };
          }
        } catch (error) {
          set({ isLoading: false });
          return { success: false, message: '登录失败，请稍后重试' };
        }
      },

      // 登出方法
      logout: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          isLoading: false
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData }
          });
        }
      }
    }),
    {
      name: 'auth-storage', // 存储键名
      storage: createJSONStorage(() => AsyncStorage), // 使用AsyncStorage进行持久化
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token
      }), // 只持久化这些字段
    }
  )
);
